<template>
  <section id="tools-section" class="bg-white rounded-lg shadow-lg py-3 px-3 lg:px-6">
    <!-- 标签页 -->
    <div class="flex border-b border-gray-200 mb-4">
      <button 
        @click="panelsStore.setActiveTab('tools')"
        :class="[
          'relative px-3 py-1 text-base transition-colors',
          panelsStore.activeTab === 'tools' ? 'font-bold text-black' : 'font-normal text-gray-500 hover:text-gray-700'
        ]"
      >
        常用工具
        <span v-if="panelsStore.activeTab === 'tools'" class="absolute left-1/2 -translate-x-1/2 bottom-0 w-6 h-0.5 bg-orange-500 rounded"></span>
      </button>
      <button 
        @click="panelsStore.setActiveTab('shopee')"
        :class="[
          'relative px-3 py-1 text-base transition-colors',
          panelsStore.activeTab === 'shopee' ? 'font-bold text-black' : 'font-normal text-gray-500 hover:text-gray-700'
        ]"
      >
        Shopee拉美站点
        <span v-if="panelsStore.activeTab === 'shopee'" class="absolute left-1/2 -translate-x-1/2 bottom-0 w-6 h-0.5 bg-orange-500 rounded"></span>
      </button>
      <button 
        @click="panelsStore.setActiveTab('tiktok')"
        :class="[
          'relative px-3 py-1 text-base transition-colors',
          panelsStore.activeTab === 'tiktok' ? 'font-bold text-black' : 'font-normal text-gray-500 hover:text-gray-700'
        ]"
      >
        TikTok Shop欧美站点
        <span v-if="panelsStore.activeTab === 'tiktok'" class="absolute left-1/2 -translate-x-1/2 bottom-0 w-6 h-0.5 bg-orange-500 rounded"></span>
      </button>
    </div>

    <!-- 工具内容 -->
    <div v-show="panelsStore.activeTab === 'tools'" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-x-4 lg:gap-x-8 gap-y-2 text-sm">
      <!-- 第一行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🔄</span>在线汇率换算
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🏆</span>1688以图搜商款
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📊</span>虾皮在线价格
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🔍</span>在线单位换算
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💡</span>关键词热度查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">😊</span>在线Emoji图案
      </a>

      <!-- 第二行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🔍</span>菲律宾热搜词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇺🇸</span>泰国热搜词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇺🇸</span>马来西亚热搜词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇻🇳</span>新加坡热搜词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇨🇳</span>中国台湾热搜词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇲🇽</span>墨西哥热搜词查询
      </a>

      <!-- 第三行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">👍</span>优选好评计算
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">⚖️</span>百度翻译
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🔄</span>谷歌翻译
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">👥</span>阿里图片翻译
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💰</span>大小写转换
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">👑</span>标题组合器
      </a>

      <!-- 第四行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🎭</span>多国翻译神器
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🥇</span>各国定价表
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💳</span>仓库关联方式
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📅</span>跨境卖家日历
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📝</span>作图神器
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🏪</span>快捷查询
      </a>

      <!-- 第五行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">💯</span>免费开店咨询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">⚡</span>虾皮店铺ID获取
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📋</span>词频统计
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🔍</span>海关编码查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📊</span>商标分类表
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📋</span>去除重复文本
      </a>

      <!-- 第六行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🔍</span>菲律宾关键词查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📋</span>平台联系方式
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💚</span>带货达人黑名单
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💎</span>船舶位置查询
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🌍</span>东南亚地图
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🗺️</span>船舶位置查询
      </a>
    </div>
    
    <!-- Shopee拉美站点内容 -->
    <div v-show="panelsStore.activeTab === 'shopee'" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-x-4 lg:gap-x-8 gap-y-2 text-sm">
      <!-- 第一行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🇨🇴</span>哥伦比亚站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇨🇱</span>智利站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇵🇱</span>波兰站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇪🇸</span>西班牙站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇨🇴</span>哥伦比亚后台
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇨🇱</span>智利后台
      </a>

      <!-- 第二行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🇵🇱</span>波兰后台
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇪🇸</span>西班牙后台
      </a>
    </div>

    <!-- TikTok Shop欧美站点内容 -->
    <div v-show="panelsStore.activeTab === 'tiktok'" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-x-4 lg:gap-x-8 gap-y-2 text-sm">
      <!-- 第一行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🇺🇸</span>美国站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇬🇧</span>英国站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇵🇱</span>波兰站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇪🇸</span>西班牙站
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇺🇸</span>美国后台
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇬🇧</span>英国后台
      </a>

      <!-- 第二行 -->
      <a href="#" class="tool-link">
        <span class="mr-2">🇵🇱</span>波兰后台
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🇪🇸</span>西班牙后台
      </a>
    </div>
  </section>
</template>

<script setup lang="ts">
import { usePanelsStore } from '@/stores/panels'

const panelsStore = usePanelsStore()
</script>

<style scoped>
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

button {
  background: none;
  border: none;
  outline: none;
  position: relative;
}

.bg-orange-100 {
  background-color: #FFF7ED;
}

.tool-link {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  color: #1f2937; /* 黑色文本 */
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid transparent;
}

.tool-link:hover {
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}
</style> 