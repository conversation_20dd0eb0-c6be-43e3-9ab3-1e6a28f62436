<template>
  <section id="market-data-section" class="bg-white rounded-lg shadow-lg py-3 px-3 lg:px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">市场分析</h3>
      <!-- 市场分析工具网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1">
          <!-- 印尼市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">印</span>
            </div>
            <span class="text-sm text-gray-700">印尼市场</span>
          </div>

          <!-- 马来市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">马</span>
            </div>
            <span class="text-sm text-gray-700">马来市场</span>
          </div>

          <!-- 泰国市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">泰</span>
            </div>
            <span class="text-sm text-gray-700">泰国市场</span>
          </div>

          <!-- 菲律宾市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-green-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">菲</span>
            </div>
            <span class="text-sm text-gray-700">菲律宾市场</span>
          </div>

          <!-- 越南市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">越</span>
            </div>
            <span class="text-sm text-gray-700">越南市场</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1">
          <!-- 新加坡市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-cyan-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">新</span>
            </div>
            <span class="text-sm text-gray-700">新加坡市场</span>
          </div>

          <!-- 巴西市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-yellow-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">巴</span>
            </div>
            <span class="text-sm text-gray-700">巴西市场</span>
          </div>

          <!-- 墨西哥市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-pink-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">墨</span>
            </div>
            <span class="text-sm text-gray-700">墨西哥市场</span>
          </div>

          <!-- 智利市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-indigo-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">智</span>
            </div>
            <span class="text-sm text-gray-700">智利市场</span>
          </div>

          <!-- 哥伦比亚市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-teal-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">哥</span>
            </div>
            <span class="text-sm text-gray-700">哥伦比亚市场</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1">
          <!-- 中国台湾市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-emerald-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">台</span>
            </div>
            <span class="text-sm text-gray-700">中国台湾市场</span>
          </div>

          <!-- 印度市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-amber-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">印</span>
            </div>
            <span class="text-sm text-gray-700">印度市场</span>
          </div>

          <!-- 更多市场 -->
          <div class="flex items-center px-1 py-1 rounded-lg hover:bg-white hover:shadow-sm transition-all cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <span class="text-sm text-gray-700">更多市场</span>
          </div>

          <!-- 空占位 -->
          <div class="invisible flex items-center px-1 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>

          <!-- 空占位 -->
          <div class="invisible flex items-center px-1 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 市场分析面板组件
</script>

<style scoped>
.transition-all {
  transition: all 0.2s ease;
}
</style>
