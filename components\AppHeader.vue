<template>
  <header class="bg-white border-b border-gray-200">
    <div class="w-full lg:w-4/5 mx-auto px-4">
      <div class="flex items-center justify-between h-12">
        <!-- Logo -->
        <div class="flex items-center">
          <img src="/images/dny123.png" alt="DNY123" class="h-6">
        </div>

        <!-- 桌面端主导航 -->
        <nav class="hidden lg:flex items-center space-x-5">
          <NuxtLink to="/" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-orange-500 hover:bg-orange-50 rounded transition-colors">首页</NuxtLink>
          <div class="relative group">
            <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 flex items-center border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">
              跨境头条
              <svg class="w-2.5 h-2.5 ml-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
              </svg>
            </a>
            <div class="absolute left-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg hidden group-hover:block z-50">
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">跨境百科</a>
            </div>
          </div>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">找活动</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">找物流</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">找服务</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">找海外仓</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">找机构</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">看直播</a>
          <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">跨境报告</a>
          <div class="relative group">
            <a href="#" class="text-gray-700 hover:text-orange-500 text-sm px-1 py-0.5 flex items-center border-b-2 border-transparent hover:border-orange-500 hover:bg-orange-50 rounded transition-colors">
              工具箱
              <svg class="w-2.5 h-2.5 ml-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
              </svg>
            </a>
            <div class="absolute left-0 top-full mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg hidden group-hover:block z-50">
              <div class="p-2">
                <div class="text-xs text-gray-500 font-medium mb-2">热门工具</div>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">关键词热度工具</a>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">本土定价表计算</a>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">抓取店铺和产品ID</a>
                <div class="text-xs text-gray-500 font-medium mb-2 mt-3">推荐工具</div>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">跨境日历</a>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">人民币外汇牌价</a>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 rounded">一词多国翻译</a>
              </div>
            </div>
        </div>
      </nav>

        <!-- 移动端菜单按钮 -->
        <div class="lg:hidden">
          <button @click="toggleMobileMenu" class="p-2 text-gray-600 hover:text-gray-900">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path v-if="!showMobileMenu" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 桌面端右侧工具 -->
        <div class="hidden lg:flex items-center space-x-3">
          <div class="text-xs text-gray-600">
            热门搜索: <a href="#" class="text-orange-500 hover:underline">万里汇</a>
          </div>
          <button class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600">
            登录
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端下拉菜单 -->
    <div v-if="showMobileMenu" class="lg:hidden bg-white border-t border-gray-200 shadow-lg">
      <div class="px-4 py-2 space-y-1">
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">跨境头条</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">找活动</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">找物流</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">找服务</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">找海外仓</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">找机构</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">看直播</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">跨境报告</a>
        <a href="#" class="block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors">工具箱</a>

        <!-- 移动端登录按钮 -->
        <div class="pt-3 border-t border-gray-200 mt-3">
          <button class="w-full bg-orange-500 text-white py-2 px-4 rounded text-sm hover:bg-orange-600 transition-colors">
            登录
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref } from 'vue'

// 移动端菜单状态
const showMobileMenu = ref(false)

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 点击外部关闭菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}
</script>

<style scoped>
/* 动画效果 */
.transition-colors {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}
</style>
