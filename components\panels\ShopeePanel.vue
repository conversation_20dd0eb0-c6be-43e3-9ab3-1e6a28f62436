<template>
  <section id="shopee-section" class="bg-white rounded-lg shadow-lg p-3 lg:p-4">
    <!-- 响应式网格：移动端3列，平板4列，桌面12列 -->
    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-12 gap-2">
      <!-- Shopee站点教程 (12个) -->
      <a href="#" class="panel-item">
        <img src="/images/countries/ph_flag.png" alt="菲律宾" class="panel-icon">
        <span class="panel-text">菲律宾站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/tw_flag.png" alt="中国台湾" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国台湾站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/my_flag.png" alt="马来西亚" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/th_flag.png" alt="泰国" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/id_flag.png" alt="印尼" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/vn_flag.png" alt="越南" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/sg_flag.png" alt="新加坡" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/br_flag.png" alt="巴西" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">巴西站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/mx_flag.png" alt="墨西哥" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">墨西哥站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <img src="/images/countries/cn_flag.png" alt="中国" class="w-12 h-12 mb-1.5 object-cover rounded-full">
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国卖家中心</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">虾皮知识大纲</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-green-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">入</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Shopee入驻</span>
      </a>

      <!-- Shopee后台入口 (12个) -->
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">菲律宾后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国台湾后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">巴西后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">S</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">墨西哥后台</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-blue-500 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">紫</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">紫鸟跨境浏览器</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-black rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">T</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">TikTok选品工具</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-green-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">周</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">市场周报</span>
      </a>
    </div>
  </section>
</template>

<script setup lang="ts">
// Shopee面板组件
// 包含24个Shopee相关链接：12个站点 + 12个后台入口
</script>

<style scoped>
/* 面板样式 */
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* 响应式面板项样式 */
.panel-item {
  @apply flex flex-col items-center p-2 lg:p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow;
}

.panel-icon {
  @apply w-10 h-10 sm:w-12 sm:h-12 mb-1 lg:mb-1.5 object-cover rounded-full;
}

.panel-text {
  @apply text-xs lg:text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 