<template>
  <div class="search-section pt-3 lg:pt-8 pb-3 lg:pb-4">
    <div class="max-w-[1600px] mx-auto px-2 sm:px-3 lg:px-4 mb-2">
      <!-- 响应式Logo和搜索框容器 -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3 lg:mb-4 space-y-3 lg:space-y-0">
        <!-- DNY123 Logo -->
        <div class="flex justify-center lg:justify-start flex-shrink-0 lg:mr-4">
          <img src="/images/logo4.png" alt="DNY123" class="h-14 sm:h-16 lg:h-20">
        </div>

        <!-- 搜索框区域 -->
        <div class="flex-1 w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto lg:mx-0">
          <!-- 搜索引擎按钮 - 水平滚动 -->
          <div class="mb-2 lg:mb-3">
            <div class="flex items-center gap-1 sm:gap-2 lg:gap-3 overflow-x-auto scrollbar-hide search-engines">
              <button 
                v-for="engine in searchStore.searchEngines" 
                :key="engine.name"
                @click="searchStore.selectEngine(engine)"
                :class="[
                  'px-2 lg:px-3 py-1 text-xs lg:text-sm transition-all duration-200 relative whitespace-nowrap flex-shrink-0',
                  searchStore.selectedEngine?.name === engine.name ? 'text-black font-medium' : 'text-gray-500 hover:text-gray-700'
                ]"
              >
                {{ engine.name }}
                <!-- 选中状态的下划线 -->
                <div 
                  v-if="searchStore.selectedEngine?.name === engine.name"
                  class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-black"
                ></div>
              </button>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="relative transition-all duration-200 bg-white rounded-lg border-2 border-blue-200" :class="searchStore.isInputFocused ? 'border-blue-300' : ''">
            <input
              v-model="searchStore.searchQuery"
              type="text"
              :placeholder="searchStore.selectedEngine?.placeholder || '请输入搜索'"
              class="w-full px-2 sm:px-3 lg:px-4 py-2 lg:py-3 text-sm lg:text-base rounded-lg focus:outline-none bg-white"
              @focus="searchStore.setInputFocused(true)"
              @blur="searchStore.setInputFocused(false)"
              @keyup.enter="searchStore.handleSearch"
            >
            <button
              @click="searchStore.handleSearch"
              class="absolute right-1 lg:right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 p-1 lg:p-1.5 transition-colors"
            >
              <svg class="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 占位符保持平衡 - 仅桌面端显示 -->
        <div class="hidden lg:block flex-shrink-0 w-32"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useSearchStore } from '~/stores/search'

const searchStore = useSearchStore()

onMounted(() => {
  searchStore.initializeSearch()
})
</script>

<style scoped>
/* 自定义滚动条 */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 搜索引擎按钮优化 */
@media (max-width: 640px) {
  .search-engines {
    gap: 0.25rem;
    padding: 0 0.25rem;
  }

  /* 移动端搜索引擎按钮更紧凑 */
  .search-engines button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  /* 确保搜索框不溢出 */
  .search-section {
    min-width: 0;
  }

  /* 移动端搜索框容器优化 */
  .search-section input {
    min-width: 0;
    width: 100%;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .search-engines {
    gap: 0.5rem;
  }
}
</style> 