<template>
  <section id="news-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">跨境资讯</h3>
      <!-- 标签页 -->
      <div class="flex items-center gap-4">
        <button 
          @click="panelsStore.setNewsTab('headlines')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'headlines' 
              ? 'text-red-600' 
              : 'text-gray-600 hover:text-red-600'
          ]"
        >
          <span class="w-3 h-3 bg-red-600 rounded-sm flex items-center justify-center text-white text-xs">X</span>
          东南亚头条
        </button>
        <button 
          @click="panelsStore.setNewsTab('tiktok')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'tiktok' 
              ? 'text-black' 
              : 'text-gray-600 hover:text-black'
          ]"
        >
          <span class="w-3 h-3 bg-black rounded-sm flex items-center justify-center text-white text-xs">⚡</span>
          TikTok知识大纲
        </button>
        <button 
          @click="panelsStore.setNewsTab('shopee')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'shopee' 
              ? 'text-orange-600' 
              : 'text-gray-600 hover:text-orange-600'
          ]"
        >
          <span class="w-3 h-3 bg-orange-600 rounded-sm flex items-center justify-center text-white text-xs">S</span>
          Shopee知识大纲
        </button>
        <button 
          @click="panelsStore.setNewsTab('lazada')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'lazada' 
              ? 'text-blue-800' 
              : 'text-gray-600 hover:text-blue-800'
          ]"
        >
          <span class="w-3 h-3 bg-blue-800 rounded-sm flex items-center justify-center text-white text-xs">L</span>
          Lazada知识大纲
        </button>
        <button 
          @click="panelsStore.setNewsTab('facebook')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'facebook' 
              ? 'text-blue-600' 
              : 'text-gray-600 hover:text-blue-600'
          ]"
        >
          <span class="w-3 h-3 bg-blue-600 rounded-sm flex items-center justify-center text-white text-xs">f</span>
          FACEBOOK
        </button>
        <button 
          @click="panelsStore.setNewsTab('zhiwuwuyan')"
          :class="[
            'flex items-center gap-2 px-2 py-1 text-xs transition-colors',
            panelsStore.newsTab === 'zhiwuwuyan' 
              ? 'text-blue-500' 
              : 'text-gray-600 hover:text-blue-500'
          ]"
        >
          <span class="w-3 h-3 bg-blue-500 rounded-sm flex items-center justify-center text-white text-xs">W</span>
          知无不言
        </button>
      </div>
    </div>

    <!-- 新闻内容区域 -->
    <!-- <div class="min-h-[200px]">
      <div v-if="panelsStore.newsTab === 'headlines'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <h4 class="text-sm font-medium text-gray-800 mb-2">东南亚电商最新动态</h4>
          <p class="text-xs text-gray-600">了解最新的东南亚跨境电商政策和市场变化...</p>
        </div>
        <div class="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <h4 class="text-sm font-medium text-gray-800 mb-2">平台政策更新</h4>
          <p class="text-xs text-gray-600">各大电商平台最新政策解读和应对策略...</p>
        </div>
        <div class="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <h4 class="text-sm font-medium text-gray-800 mb-2">市场趋势分析</h4>
          <p class="text-xs text-gray-600">深度分析东南亚市场发展趋势和机遇...</p>
        </div>
      </div>
      
      <div v-else-if="panelsStore.newsTab === 'tiktok'" class="space-y-3">
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📚</span>TikTok开店指南
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">💡</span>TikTok运营技巧
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📊</span>TikTok数据分析
        </div>
      </div>
      
      <div v-else-if="panelsStore.newsTab === 'shopee'" class="space-y-3">
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">🛍️</span>Shopee店铺运营
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">🎯</span>Shopee广告投放
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📈</span>Shopee数据分析
        </div>
      </div>
      
      <div v-else-if="panelsStore.newsTab === 'lazada'" class="space-y-3">
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">🏪</span>Lazada开店流程
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">💼</span>Lazada商品管理
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📋</span>Lazada政策解读
        </div>
      </div>
      
      <div v-else-if="panelsStore.newsTab === 'facebook'" class="space-y-3">
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📱</span>Facebook广告投放
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">👥</span>Facebook社群营销
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">📊</span>Facebook数据分析
        </div>
      </div>
      
      <div v-else-if="panelsStore.newsTab === 'zhiwuwuyan'" class="space-y-3">
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">💬</span>知无不言社区
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">🤝</span>经验分享
        </div>
        <div class="flex items-center text-blue-600 hover:underline cursor-pointer">
          <span class="mr-2">❓</span>问题解答
        </div>
      </div>
    </div> -->
  </section>
</template>

<script setup lang="ts">
import { usePanelsStore } from '~/stores/panels'

const panelsStore = usePanelsStore()
</script>

<style scoped>
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 