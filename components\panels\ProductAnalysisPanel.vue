<template>
  <section id="product-analysis-section" class="bg-white rounded-lg shadow-lg py-3 px-3 lg:px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">选品分析</h3>
      
      <!-- 选品分析工具网格 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
        <!-- Shopee选品专家 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">Shopee选品专家</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">数据提供/产品调研/店铺监控站点分析</p>
        </div>
        
        <!-- Kalodata -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">K</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">Kalodata</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">全球市场覆盖率第一的TikTok & Shopee电商数据分析平台</p>
        </div>
        
        <!-- Shopdora虾皮数据 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">Shopdora虾皮数据</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">Shopee选品工具 | 关键词挖掘 | 流量分析</p>
        </div>
        
        <!-- FastMoss -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-pink-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">F</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">FastMoss</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">70万商家首选的TikTok数据分析工具</p>
        </div>
        
        <!-- 嗨哆狗数据 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">🐶</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">嗨哆狗数据</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">帮助跨境商家挖掘TikTok潜力商品</p>
        </div>
        
        <!-- 友禹数据 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-yellow-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">Y</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">友禹数据</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">免费数据/商品分析/店铺分析类目数据</p>
        </div>
        
        <!-- Echo Tik -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-indigo-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">E</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">Echo Tik</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">寻找热分析达人/商品分析/视频和直播间分析</p>
        </div>
        
        <!-- 特看Tabcut -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">特看Tabcut</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">TikTok专业选品工具，点击领新用户福利</p>
        </div>
        
        <!-- 谷歌趋势 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">G</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">谷歌趋势</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">查看关键词在Google的搜索次数及变化趋势</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 选品分析面板组件
</script>

<style scoped>
/* 行文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
}

/* 统一的软件项目hover效果 */
.software-item {
  transition: all 0.2s ease-in-out;
}

.software-item:hover {
  background-color: #dbeafe !important;
}

.software-item:hover .software-title {
  color: #f97316 !important;
}

.software-item:hover .software-desc {
  color: #111827 !important;
}

/* 通用group hover效果 - 适用于所有软件项目 */
.group {
  transition: all 0.2s ease-in-out;
}

.group:hover {
  background-color: #dbeafe !important;
}

.group:hover h4 {
  color: #f97316 !important;
}

.group:hover p {
  color: #111827 !important;
}
</style> 