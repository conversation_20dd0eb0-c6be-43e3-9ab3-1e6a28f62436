<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2 mb-4">
    <!-- 水平滚动的标签栏 -->
    <div class="flex overflow-x-auto scrollbar-hide gap-2">
      <button 
        v-for="item in navigationStore.navigationItems" 
        :key="item.id"
        @click="navigationStore.scrollToSection(item.id)"
        :class="[
          'px-3 py-2 text-sm font-medium rounded-md whitespace-nowrap flex-shrink-0 transition-colors',
          navigationStore.activeSection === item.id 
            ? 'bg-orange-500 text-white' 
            : 'text-gray-600 hover:bg-gray-100'
        ]"
      >
        {{ item.name }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { useNavigationStore } from '~/stores/navigation'

const navigationStore = useNavigationStore()
</script>

<style scoped>
/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
</style>
