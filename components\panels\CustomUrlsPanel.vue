<template>
  <section id="custom-urls-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-6">
    <div class="flex items-center justify-between mb-4">
      <!-- 标签页 -->
      <div class="flex items-center gap-1">
        <button 
          @click="panelsStore.setCustomTab('website')"
          :class="[
            'px-3 py-1 text-xs rounded-full transition-colors',
            panelsStore.customTab === 'website' 
              ? 'bg-blue-500 text-white' 
              : 'text-gray-600 hover:text-blue-500'
          ]"
        >
          自定义网址
        </button>
        <button 
          @click="panelsStore.setCustomTab('leisure')"
          :class="[
            'px-3 py-1 text-xs rounded-full transition-colors',
            panelsStore.customTab === 'leisure' 
              ? 'bg-blue-500 text-white' 
              : 'text-gray-600 hover:text-blue-500'
          ]"
        >
          生活休闲
        </button>
      </div>
      <button class="text-blue-600 hover:text-blue-800 text-xs">+ 添加</button>
    </div>
    
    <!-- 自定义网址内容 -->
    <div v-if="panelsStore.customTab === 'website'" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-x-4 lg:gap-x-8 gap-y-2 text-sm">
      <a href="#" class="tool-link">
        <span class="mr-2">🔷</span>微博
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📚</span>知乎
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📧</span>163邮箱
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📄</span>金山文档
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🛒</span>淘宝网
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🌐</span>紫鸟浏览器
      </a>
    </div>

    <!-- 生活休闲内容 -->
    <div v-if="panelsStore.customTab === 'leisure'" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-x-4 lg:gap-x-8 gap-y-2 text-sm">
      <a href="#" class="tool-link">
        <span class="mr-2">🎵</span>网易云音乐
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📺</span>哔哩哔哩
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🎬</span>爱奇艺
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🍔</span>美团外卖
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🛍️</span>京东商城
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">✈️</span>携程旅行
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📰</span>今日头条
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🎮</span>Steam
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">📖</span>起点中文网
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🏠</span>贝壳找房
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">🚗</span>汽车之家
      </a>
      <a href="#" class="tool-link">
        <span class="mr-2">💰</span>支付宝
      </a>
    </div>
  </section>
</template>

<script setup lang="ts">
import { usePanelsStore } from '~/stores/panels'

const panelsStore = usePanelsStore()
</script>

<style scoped>
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.tool-link {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  color: #1f2937; /* 黑色文本 */
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid transparent;
}

.tool-link:hover {
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}
</style> 