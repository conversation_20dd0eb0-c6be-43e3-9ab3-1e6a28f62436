<template>
  <section id="lazada-section" class="bg-white rounded-lg shadow-lg p-3 lg:p-4">
    <!-- 响应式网格：移动端3列，平板4列，桌面12列 -->
    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-12 gap-2">
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada马来站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada新加坡站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada泰国站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada印尼站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada菲律宾站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-orange-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">L</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada越南站</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-blue-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">卖</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">卖家中心</span>
      </a>
      <a href="#" class="flex flex-col items-center p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow">
        <div class="w-12 h-12 mb-1.5 bg-green-600 rounded-full flex items-center justify-center">
          <span class="text-white text-sm font-bold">入</span>
        </div>
        <span class="text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada入驻</span>
      </a>
    </div>
  </section>
</template>

<script setup lang="ts">
// Lazada面板组件
</script>

<style scoped>
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 