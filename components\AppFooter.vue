<template>
  <footer class="bg-gray-800 text-white py-8 lg:py-12">
    <div class="max-w-[1600px] mx-auto px-4">
      <!-- 移动端：垂直堆叠，桌面端：三列布局 -->
      <div class="space-y-8 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-8">
        <!-- 关于我们 -->
        <div class="text-center lg:text-left">
          <h3 class="text-lg lg:text-xl font-bold mb-4">关于</h3>
          <p class="text-gray-300 leading-relaxed mb-4 text-sm">
            DNY123是一家专注东南亚跨境电商的导航网站，致力为卖家打破信息差和资源差，围绕Shopee、Lazada和TikTok Shop等东南亚平台，及时收录东南亚卖家运营必备工具、汇集服务生态资源等必备跨境服务，力求中立、客观、专业。
          </p>
          <p class="text-gray-300 text-sm">
            东南亚出海，就上DNY123。
          </p>
        </div>
        
        <!-- 链接部分 -->
        <div class="text-center">
          <h3 class="text-lg lg:text-xl font-bold mb-4">链接</h3>
          <div class="flex flex-col gap-2 text-sm">
            <a href="#" class="text-gray-300 hover:text-white transition-colors">关于我们</a>
            <a href="#" class="text-gray-300 hover:text-white transition-colors">联系我们</a>
            <a href="#" class="text-gray-300 hover:text-white transition-colors">免责声明</a>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="flex justify-center lg:justify-end">
          <!-- 移动端：2x2网格，桌面端：水平排列 -->
          <div class="grid grid-cols-2 gap-4 lg:flex lg:gap-4">
            <!-- 公众号 -->
            <div class="text-center">
              <div class="w-20 h-20 lg:w-24 lg:h-24 bg-white rounded mx-auto mb-2 flex items-center justify-center">
                <img :src="qrcodePublic" alt="公众号二维码" class="w-18 h-18 lg:w-22 lg:h-22 rounded">
              </div>
              <p class="text-xs text-gray-300">公众号</p>
            </div>

            <!-- 资源对接 -->
            <div class="text-center">
              <div class="w-20 h-20 lg:w-24 lg:h-24 bg-white rounded mx-auto mb-2 flex items-center justify-center">
                <img :src="qrcodeResource" alt="资源对接二维码" class="w-18 h-18 lg:w-22 lg:h-22 rounded">
              </div>
              <p class="text-xs text-gray-300">资源对接</p>
            </div>

            <!-- 卖家社群 -->
            <div class="text-center">
              <div class="w-20 h-20 lg:w-24 lg:h-24 bg-white rounded mx-auto mb-2 flex items-center justify-center">
                <img :src="qrcodeGroup" alt="卖家社群二维码" class="w-18 h-18 lg:w-22 lg:h-22 rounded">
              </div>
              <p class="text-xs text-gray-300">卖家社群</p>
            </div>

            <!-- 服务商商务合作 -->
            <div class="text-center">
              <div class="w-20 h-20 lg:w-24 lg:h-24 bg-white rounded mx-auto mb-2 flex items-center justify-center">
                <img :src="qrcodeBusiness" alt="服务商商务合作二维码" class="w-18 h-18 lg:w-22 lg:h-22 rounded">
              </div>
              <p class="text-xs text-gray-300">服务商商务合作</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="border-t border-gray-700 mt-8 pt-4">
        <div class="flex items-center justify-center gap-2 text-xs text-gray-400 mb-3">
          <span class="bg-gray-700 px-2 py-1 rounded text-xs">Copyright</span>
          <span>Copyright © 2016-2024 | 东南亚导航 | 闽ICP备2022007330号</span>
        </div>
        <div class="text-xs text-gray-500 text-center">
          声明：网站上的服务均为第三方提供，与DNY123无关。请用户注意甄别服务质量，避免上当受骗。
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// 导入二维码图片
import qrcodePublic from '/images/1714273926548717_ab81160d39a5821c1bed2a04ffb31c1.jpeg'
import qrcodeResource from '/images/wechat-assistant.jpeg'
import qrcodeGroup from '/images/1713528678645557_kefu.png'
import qrcodeBusiness from '/images/qiye-wechat.png'
</script>

<style scoped>
/* 动画效果 */
.transition-colors {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* 悬浮效果 */
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>
