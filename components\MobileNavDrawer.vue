<template>
  <!-- 移动端导航抽屉 -->
  <div v-if="isOpen" class="fixed inset-0 z-50 lg:hidden">
    <!-- 遮罩层 -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="close"
    ></div>
    
    <!-- 抽屉内容 -->
    <div class="fixed left-0 top-0 h-full w-80 bg-white shadow-xl transform transition-transform duration-300 ease-in-out">
      <div class="flex flex-col h-full">
        <!-- 抽屉头部 -->
        <div class="flex justify-between items-center p-4 border-b border-gray-200">
          <div class="flex items-center">
            <img src="/images/dny123.png" alt="DNY123" class="h-6 mr-2">
            <h2 class="text-lg font-bold text-gray-900">导航菜单</h2>
          </div>
          <button 
            @click="close" 
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <!-- 导航项列表 -->
        <div class="flex-1 overflow-y-auto p-4">
          <div class="space-y-2">
            <a 
              v-for="item in navigationStore.navigationItems" 
              :key="item.id"
              :href="item.href"
              @click="handleNavClick(item.id)"
              :class="[
                'flex items-center py-3 px-4 rounded-lg transition-colors',
                navigationStore.activeSection === item.id 
                  ? 'bg-orange-500 text-white' 
                  : 'text-gray-700 hover:bg-gray-100'
              ]"
            >
              <span class="text-sm font-medium">{{ item.name }}</span>
              <svg v-if="navigationStore.activeSection === item.id" 
                   class="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
        
        <!-- 抽屉底部 -->
        <div class="p-4 border-t border-gray-200">
          <button class="w-full bg-orange-500 text-white py-3 px-4 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors">
            登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useNavigationStore } from '~/stores/navigation'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

const navigationStore = useNavigationStore()

// 关闭抽屉
const close = () => {
  emit('close')
}

// 处理导航点击
const handleNavClick = (sectionId) => {
  navigationStore.scrollToSection(sectionId)
  close() // 点击后关闭抽屉
}
</script>

<style scoped>
/* 抽屉动画 */
.transition-transform {
  transition: transform 0.3s ease-in-out;
}

/* 确保抽屉在最顶层 */
.z-50 {
  z-index: 50;
}
</style>
