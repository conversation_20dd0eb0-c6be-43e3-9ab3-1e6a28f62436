<template>
  <section id="payment-section" class="bg-white rounded-lg shadow-lg py-3 px-3 lg:px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">收款方式</h3>
      <!-- 收款方式网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1.5">
          <!-- 连连本土回款 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">连</span>
            </div>
            <span class="text-sm text-gray-700">连连本土回款</span>
          </div>

          <!-- <PERSON>sher开时支付 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">K</span>
            </div>
            <span class="text-sm text-gray-700">Ksher开时支付</span>
          </div>

          <!-- Pingpong -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-cyan-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">P</span>
            </div>
            <span class="text-sm text-gray-700">Pingpong</span>
          </div>

          <!-- Payoneer派安盈 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">P</span>
            </div>
            <span class="text-sm text-gray-700">Payoneer派安盈</span>
          </div>

          <!-- 寻汇SUNRATE -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-700 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">寻</span>
            </div>
            <span class="text-sm text-gray-700">寻汇SUNRATE</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1.5">
          <!-- 珊瑚跨境 CoralGlo... -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-pink-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">珊</span>
            </div>
            <span class="text-sm text-gray-700">珊瑚跨境 CoralGlo...</span>
          </div>

          <!-- 万里汇WorldFirst -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-green-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">万</span>
            </div>
            <span class="text-sm text-gray-700">万里汇WorldFirst</span>
          </div>

          <!-- 空雨科技 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">空</span>
            </div>
            <span class="text-sm text-gray-700">空雨科技</span>
          </div>

          <!-- 汇付天下 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">汇</span>
            </div>
            <span class="text-sm text-gray-700">汇付天下</span>
          </div>

          <!-- 易宝支付 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-yellow-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">易</span>
            </div>
            <span class="text-sm text-gray-700">易宝支付</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1.5">
          <!-- 钱海 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-indigo-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">钱</span>
            </div>
            <span class="text-sm text-gray-700">钱海</span>
          </div>

          <!-- 银联国际 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">银</span>
            </div>
            <span class="text-sm text-gray-700">银联国际</span>
          </div>

          <!-- 跨境收付 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-teal-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">跨</span>
            </div>
            <span class="text-sm text-gray-700">跨境收付</span>
          </div>

          <!-- 环球快客 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-emerald-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">环</span>
            </div>
            <span class="text-sm text-gray-700">环球快客</span>
          </div>

          <!-- 其他收款方式 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <span class="text-sm text-gray-700">更多收款方式</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 收款方式面板组件
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style>