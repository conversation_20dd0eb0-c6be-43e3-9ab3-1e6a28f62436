/* 移动端响应式样式 */

/* 通用面板响应式样式 */
@layer components {
  .panel-responsive {
    @apply p-3 lg:p-4;
  }
  
  .panel-grid-responsive {
    @apply grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-12 gap-2;
  }
  
  .panel-grid-5-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-1.5;
  }
  
  .panel-grid-6-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2;
  }
  
  .panel-grid-8-responsive {
    @apply grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 lg:gap-4;
  }
  
  .panel-item-responsive {
    @apply flex flex-col items-center p-2 lg:p-1.5 bg-white rounded-lg hover:shadow-md transition-shadow;
  }
  
  .panel-icon-responsive {
    @apply w-10 h-10 sm:w-12 sm:h-12 mb-1 lg:mb-1.5 object-cover rounded-full;
  }
  
  .panel-text-responsive {
    @apply text-xs lg:text-xs text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full;
  }
}

/* 移动端导航样式 */
@layer components {
  .mobile-nav-item {
    @apply block py-3 px-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors;
  }
  
  .mobile-tab-button {
    @apply px-3 py-2 text-sm font-medium rounded-md whitespace-nowrap flex-shrink-0 transition-colors;
  }
  
  .mobile-tab-active {
    @apply bg-orange-500 text-white;
  }
  
  .mobile-tab-inactive {
    @apply text-gray-600 hover:bg-gray-100;
  }
}

/* 响应式容器 */
@layer components {
  .responsive-container {
    @apply max-w-[1600px] mx-auto px-4;
  }
  
  .responsive-flex {
    @apply lg:flex lg:gap-0 relative;
  }
  
  .responsive-sidebar {
    @apply hidden lg:block w-40 flex-shrink-0 relative;
  }
  
  .responsive-main {
    @apply flex-1 min-w-0;
  }
}

/* 搜索区域响应式 */
@layer components {
  .search-responsive-container {
    @apply flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-4 lg:space-y-0;
  }
  
  .search-responsive-logo {
    @apply flex justify-center lg:justify-start flex-shrink-0 lg:mr-4;
  }
  
  .search-responsive-form {
    @apply flex-1 max-w-2xl mx-auto lg:mx-0;
  }
  
  .search-responsive-input {
    @apply w-full px-4 py-2 lg:py-3 text-sm lg:text-base rounded-lg focus:outline-none bg-white;
  }
  
  .search-responsive-engines {
    @apply flex items-center gap-2 lg:gap-3 overflow-x-auto scrollbar-hide;
  }
}

/* Footer 响应式 */
@layer components {
  .footer-responsive-grid {
    @apply space-y-8 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-8;
  }
  
  .footer-responsive-about {
    @apply text-center lg:text-left;
  }
  
  .footer-responsive-qr {
    @apply flex justify-center lg:justify-end;
  }
  
  .footer-responsive-qr-grid {
    @apply grid grid-cols-2 gap-4 lg:flex lg:gap-4;
  }
  
  .footer-responsive-qr-item {
    @apply w-20 h-20 lg:w-24 lg:h-24 bg-white rounded mx-auto mb-2 flex items-center justify-center;
  }
  
  .footer-responsive-qr-image {
    @apply w-18 h-18 lg:w-22 lg:h-22 rounded;
  }
}

/* 隐藏滚动条 */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
  
  /* 移动端面板间距调整 */
  .panel-mobile-spacing {
    @apply mt-2;
  }
  
  /* 移动端文字大小调整 */
  .text-mobile-adjust {
    @apply text-sm;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}
